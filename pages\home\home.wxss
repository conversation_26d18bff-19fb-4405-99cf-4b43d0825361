.page {
  padding: 0 59.3rpx;
  background-color: #ffffff;
  width: 100%;
  height: 100vh;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  box-sizing: border-box;
}

.logo-group {
  padding-bottom: 67rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: flex-start;
}

.logo-font {
  width: 498rpx;
  height: 398rpx;
}

.btn-component {
  padding: 0;
  background-image: linear-gradient(180deg, #66f790 -70.3%, #00a946 170.3%, #00da4600 172.7%);
  border-radius: 108.14rpx;
  box-shadow: 0rpx 3.49rpx 6.98rpx #00000040;
  width: 629rpx;
  height: 237rpx;
  margin-bottom: 67rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s ease;
  position: relative; /* 添加相对定位，便于内部元素精确定位 */
}

.btn-component:active {
  transform: scale(0.95);
  opacity: 0.8;
}

.btn-text {
  font-size: 174.42rpx;
  font-family: "PingFang SC", "Hiragino Sans GB", "Microsoft YaHei", sans-serif;
  line-height: 0.9; /* 调整行高，让文字在视觉上更居中 */
  font-weight: 800;
  color: #ffffff;
  text-shadow: 0rpx 3.49rpx 6.98rpx #00000040;
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0;
  padding: 0;
  /* 确保文字在按钮中垂直居中 */
  transform: translateY(-2rpx); /* 微调垂直位置，补偿字体基线偏移 */
}

.btn-image {
  width: 178rpx;
  height: 326rpx;
}

.ml-8 {
  margin-left: 8rpx;
}

/* 忽大忽小动效 - 错落感设计 */
.btn-pulse-1 {
  animation: pulse 3s infinite;
  animation-delay: 0s;
}

.btn-pulse-2 {
  animation: pulse 3s infinite;
  animation-delay: 1s;
}

.btn-pulse-3 {
  animation: pulse 3s infinite;
  animation-delay: 2s;
}

/* 脉冲动画关键帧 */
@keyframes pulse {
  0% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.03);
  }
  100% {
    transform: scale(1);
  }
}

