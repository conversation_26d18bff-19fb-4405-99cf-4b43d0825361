// 音效管理器 - 适老化小程序专用
class SoundManager {
  constructor() {
    this.audioContext = null;
    this.isEnabled = true;
    this.isInitialized = false;
    this.init();
  }

  // 初始化音频上下文
  init() {
    try {
      // 在微信小程序中，我们需要在用户交互后才能创建AudioContext
      if (typeof wx !== 'undefined') {
        // 微信小程序环境，延迟初始化
        this.isInitialized = false;
      } else {
        // Web环境
        this.audioContext = new (window.AudioContext || window.webkitAudioContext)();
        this.isInitialized = true;
      }
    } catch (e) {
      console.log('音频上下文初始化失败:', e);
    }
  }

  // 确保音频上下文已初始化
  ensureAudioContext() {
    if (!this.isInitialized && typeof wx !== 'undefined') {
      try {
        // 在微信小程序中创建音频上下文
        this.audioContext = wx.createWebAudioContext();
        this.isInitialized = true;
        console.log('微信小程序音频上下文初始化成功');
      } catch (e) {
        console.log('微信小程序音频上下文初始化失败:', e);
        this.isInitialized = false;
      }
    }
  }

  // 创建音调
  createTone(frequency, duration, type = 'sine') {
    this.ensureAudioContext();
    if (!this.audioContext || !this.isInitialized) return;

    try {
      const oscillator = this.audioContext.createOscillator();
      const gainNode = this.audioContext.createGain();

      oscillator.connect(gainNode);
      gainNode.connect(this.audioContext.destination);

      oscillator.frequency.setValueAtTime(frequency, this.audioContext.currentTime);
      oscillator.type = type;

      gainNode.gain.setValueAtTime(0.3, this.audioContext.currentTime);
      gainNode.gain.exponentialRampToValueAtTime(0.01, this.audioContext.currentTime + duration);

      oscillator.start(this.audioContext.currentTime);
      oscillator.stop(this.audioContext.currentTime + duration);
    } catch (e) {
      console.log('音调创建失败:', e);
    }
  }

  // 播放钢琴和弦（声音A - 主要功能按钮：拍字、传字、拍图、传图）
  playPianoChord() {
    if (!this.isEnabled) return;

    console.log('🎵 播放钢琴和弦音效');

    // 钢琴和弦进行 - C-E-G-C（根据traditional.html的A1音效）
    this.createTone(261.63, 0.3, 'sine'); // C4
    setTimeout(() => this.createTone(329.63, 0.3, 'sine'), 200); // E4
    setTimeout(() => this.createTone(392.00, 0.3, 'sine'), 400); // G4
    setTimeout(() => this.createTone(523.25, 0.5, 'sine'), 600); // C5

    // 添加振动反馈
    this.vibrate([100, 50, 100]);
  }

  // 播放短促滴声（声音C - 辅助功能按钮：解释、字体+/-、复制、重来、返回）
  playShortBeep() {
    if (!this.isEnabled) return;

    console.log('🔔 播放短促滴声音效');

    // 短促滴声 - 根据electronic.html的C1音效
    this.createTone(1000, 0.1, 'sine');
    setTimeout(() => this.createTone(800, 0.1, 'sine'), 100);

    // 添加振动反馈
    this.vibrate([50]);
  }

  // 播放钢琴问候（开机音效 - Splash页面）
  playPianoGreeting() {
    if (!this.isEnabled) return;

    console.log('🎹 播放钢琴问候开机音效');

    // 钢琴问候旋律 - 根据traditional.html的开机音效1
    const melody = [
      {freq: 523, duration: 0.4}, // C5
      {freq: 587, duration: 0.4}, // D5
      {freq: 659, duration: 0.4}, // E5
      {freq: 698, duration: 0.6}, // F5
      {freq: 784, duration: 0.8}  // G5
    ];

    melody.forEach((note, i) => {
      setTimeout(() => this.createTone(note.freq, note.duration, 'sine'), i * 400);
    });

    // 开机音效不需要振动，避免打扰
  }

  // 振动反馈
  vibrate(pattern) {
    try {
      if (typeof wx !== 'undefined' && wx.vibrateShort) {
        // 微信小程序环境
        wx.vibrateShort({
          type: 'medium', // 中等强度振动
          success: () => console.log('振动反馈成功'),
          fail: (err) => console.log('振动反馈失败:', err)
        });
      } else if (typeof navigator !== 'undefined' && navigator.vibrate) {
        // Web环境
        navigator.vibrate(pattern);
      }
    } catch (e) {
      console.log('振动功能不可用:', e);
    }
  }

  // 启用/禁用音效
  toggle(enabled) {
    this.isEnabled = enabled;
    console.log(`音效管理器${enabled ? '已启用' : '已禁用'}`);
  }

  // 获取音效状态
  getStatus() {
    return {
      isEnabled: this.isEnabled,
      isInitialized: this.isInitialized,
      hasAudioContext: !!this.audioContext
    };
  }
}

// 创建全局实例
const soundManager = new SoundManager();

module.exports = soundManager;