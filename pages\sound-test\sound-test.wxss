/* 音效测试页面样式 */
.container {
  padding: 40rpx;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  min-height: 100vh;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', sans-serif;
}

.header {
  text-align: center;
  margin-bottom: 60rpx;
}

.title {
  display: block;
  font-size: 48rpx;
  font-weight: bold;
  color: white;
  margin-bottom: 20rpx;
}

.status {
  display: block;
  font-size: 28rpx;
  color: rgba(255, 255, 255, 0.8);
  background: rgba(255, 255, 255, 0.1);
  padding: 16rpx 32rpx;
  border-radius: 50rpx;
  backdrop-filter: blur(10rpx);
}

.test-section, .control-section, .results-section {
  margin-bottom: 60rpx;
}

.section-title {
  display: block;
  font-size: 36rpx;
  font-weight: bold;
  color: white;
  margin-bottom: 30rpx;
  text-align: center;
}

.test-btn, .control-btn {
  width: 100%;
  margin-bottom: 30rpx;
  padding: 40rpx 30rpx;
  border-radius: 20rpx;
  border: none;
  font-size: 32rpx;
  font-weight: bold;
  color: white;
  position: relative;
  overflow: hidden;
  box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.3);
  transition: all 0.3s ease;
}

.test-btn::after, .control-btn::after {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  transition: left 0.5s;
}

.test-btn:active::after, .control-btn:active::after {
  left: 100%;
}

.startup {
  background: linear-gradient(135deg, #ff9a9e 0%, #fecfef 100%);
}

.sound-a {
  background: linear-gradient(135deg, #a8edea 0%, #fed6e3 100%);
}

.sound-c {
  background: linear-gradient(135deg, #ffecd2 0%, #fcb69f 100%);
}

.vibration {
  background: linear-gradient(135deg, #d299c2 0%, #fef9d7 100%);
}

.toggle {
  background: linear-gradient(135deg, #89f7fe 0%, #66a6ff 100%);
}

.clear {
  background: linear-gradient(135deg, #fdbb2d 0%, #22c1c3 100%);
}

.btn-desc {
  display: block;
  font-size: 24rpx;
  font-weight: normal;
  margin-top: 10rpx;
  opacity: 0.8;
}

.results-section {
  background: rgba(255, 255, 255, 0.1);
  border-radius: 20rpx;
  padding: 40rpx;
  backdrop-filter: blur(10rpx);
}

.result-item {
  background: rgba(255, 255, 255, 0.1);
  border-radius: 16rpx;
  padding: 30rpx;
  margin-bottom: 20rpx;
  border-left: 8rpx solid #00a946;
}

.result-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 10rpx;
}

.result-type {
  font-size: 28rpx;
  font-weight: bold;
  color: white;
}

.result-time {
  font-size: 24rpx;
  color: rgba(255, 255, 255, 0.7);
}

.result-desc {
  font-size: 26rpx;
  color: rgba(255, 255, 255, 0.9);
  line-height: 1.4;
}

.footer {
  text-align: center;
  margin-top: 60rpx;
}

.back-btn {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  border: 2rpx solid rgba(255, 255, 255, 0.3);
  border-radius: 50rpx;
  padding: 30rpx 60rpx;
  font-size: 32rpx;
  font-weight: bold;
  box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.3);
}
