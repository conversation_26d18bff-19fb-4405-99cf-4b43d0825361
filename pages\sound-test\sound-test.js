// 音效测试页面
Page({
  data: {
    soundStatus: '未初始化',
    testResults: []
  },

  onLoad() {
    // 获取全局音效管理器
    const app = getApp();
    this.soundManager = app.globalData.soundManager;
    
    if (this.soundManager) {
      const status = this.soundManager.getStatus();
      this.setData({
        soundStatus: `已初始化 - 启用:${status.isEnabled} 音频上下文:${status.hasAudioContext}`
      });
    }
  },

  // 测试开机音效
  testStartupSound() {
    console.log('🎹 测试开机音效');
    if (this.soundManager) {
      this.soundManager.playPianoGreeting();
      this.addTestResult('开机音效', '钢琴问候');
    }
  },

  // 测试A类音效（主要功能按钮）
  testSoundA() {
    console.log('🎵 测试A类音效');
    if (this.soundManager) {
      this.soundManager.playPianoChord();
      this.addTestResult('A类音效', '钢琴和弦 - 拍字/传字/拍图/传图按钮');
    }
  },

  // 测试C类音效（辅助功能按钮）
  testSoundC() {
    console.log('🔔 测试C类音效');
    if (this.soundManager) {
      this.soundManager.playShortBeep();
      this.addTestResult('C类音效', '短促滴声 - 解释/字体/复制/重来/返回按钮');
    }
  },

  // 测试振动功能
  testVibration() {
    console.log('📳 测试振动功能');
    if (this.soundManager) {
      this.soundManager.vibrate([100, 50, 100]);
      this.addTestResult('振动测试', '中等强度振动');
    }
  },

  // 添加测试结果
  addTestResult(type, description) {
    const timestamp = new Date().toLocaleTimeString();
    const newResult = {
      type,
      description,
      timestamp,
      id: Date.now()
    };
    
    const results = [...this.data.testResults, newResult];
    this.setData({
      testResults: results
    });
  },

  // 清空测试结果
  clearResults() {
    this.setData({
      testResults: []
    });
  },

  // 切换音效开关
  toggleSound() {
    if (this.soundManager) {
      const currentStatus = this.soundManager.getStatus();
      this.soundManager.toggle(!currentStatus.isEnabled);
      
      const newStatus = this.soundManager.getStatus();
      this.setData({
        soundStatus: `已初始化 - 启用:${newStatus.isEnabled} 音频上下文:${newStatus.hasAudioContext}`
      });
      
      this.addTestResult('音效开关', `${newStatus.isEnabled ? '已启用' : '已禁用'}`);
    }
  },

  // 返回首页
  goBack() {
    // 播放短促滴声音效
    if (this.soundManager) {
      this.soundManager.playShortBeep();
    }
    
    wx.navigateBack({
      delta: 1,
      fail: (err) => {
        console.error('返回失败:', err);
        wx.navigateTo({
          url: '/pages/home/<USER>'
        });
      }
    });
  }
});
