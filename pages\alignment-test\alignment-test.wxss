/* 导入主页面样式 */
@import "../home/<USER>";

.page {
  padding: 40rpx;
  background-color: #f5f5f5;
  min-height: 100vh;
  position: relative;
}

/* 网格背景 */
.page.show-grid::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-image: 
    linear-gradient(rgba(0,0,0,0.1) 1px, transparent 1px),
    linear-gradient(90deg, rgba(0,0,0,0.1) 1px, transparent 1px);
  background-size: 20rpx 20rpx;
  pointer-events: none;
  z-index: 1;
}

.header {
  text-align: center;
  margin-bottom: 60rpx;
  z-index: 2;
  position: relative;
}

.title {
  font-size: 48rpx;
  font-weight: bold;
  color: #333;
  display: block;
  margin-bottom: 20rpx;
}

.subtitle {
  font-size: 28rpx;
  color: #666;
  display: block;
}

.test-buttons {
  display: flex;
  flex-direction: column;
  align-items: center;
  margin-bottom: 60rpx;
  z-index: 2;
  position: relative;
}

.test-btn-container {
  margin-bottom: 40rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
}

.btn-label {
  font-size: 24rpx;
  color: #999;
  margin-top: 10rpx;
}

/* 重写按钮样式以适应测试页面 */
.test-buttons .btn-component {
  margin-bottom: 20rpx;
  z-index: 2;
  position: relative;
}

.control-panel {
  background: white;
  border-radius: 20rpx;
  padding: 40rpx;
  margin-bottom: 40rpx;
  box-shadow: 0 4rpx 12rpx rgba(0,0,0,0.1);
  z-index: 2;
  position: relative;
}

.control-section {
  margin-bottom: 40rpx;
}

.control-section:last-child {
  margin-bottom: 0;
}

.control-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  display: block;
  margin-bottom: 20rpx;
}

.control-buttons {
  display: flex;
  gap: 20rpx;
  flex-wrap: wrap;
}

.control-btn {
  padding: 16rpx 32rpx;
  border: 2rpx solid #ddd;
  border-radius: 12rpx;
  background: white;
  font-size: 28rpx;
  color: #666;
  transition: all 0.2s ease;
}

.control-btn:active {
  transform: scale(0.95);
}

.control-btn.active {
  background: #4CAF50;
  color: white;
  border-color: #4CAF50;
}

.action-buttons {
  display: flex;
  gap: 20rpx;
  margin-bottom: 40rpx;
  z-index: 2;
  position: relative;
}

.action-btn {
  flex: 1;
  padding: 24rpx;
  border-radius: 12rpx;
  font-size: 32rpx;
  font-weight: bold;
  border: none;
  transition: all 0.2s ease;
}

.action-btn:active {
  transform: scale(0.95);
}

.action-btn.primary {
  background: linear-gradient(135deg, #4CAF50, #45a049);
  color: white;
}

.action-btn.secondary {
  background: #f0f0f0;
  color: #666;
}

.params-display {
  background: #333;
  color: white;
  padding: 20rpx;
  border-radius: 12rpx;
  text-align: center;
  z-index: 2;
  position: relative;
}

.params-text {
  font-size: 24rpx;
  font-family: monospace;
}

/* Flex 布局工具类 */
.flex-row {
  display: flex;
  flex-direction: row;
}

.justify-center {
  justify-content: center;
}

.items-center {
  align-items: center;
}

.ml-8 {
  margin-left: 8rpx;
}
