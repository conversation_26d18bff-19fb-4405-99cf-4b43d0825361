Page({
  data: {
    fontSize: 52,
    showCustomModal: false,  // 客服弹窗状态
    showCopyModal: false,    // 复制弹窗状态
    isReading: false,
    audioContext: null
  },

  onLoad() {
    // 获取全局音效管理器
    const app = getApp();
    this.soundManager = app.globalData.soundManager;
  },

  // 返回首页（返回按钮 - 使用C类音效）
  goBack() {
    // 播放短促滴声音效
    if (this.soundManager) {
      this.soundManager.playShortBeep();
    }

    wx.navigateBack({
      delta: 1,
      fail: (err) => {
        console.error('返回失败:', err);
        wx.navigateTo({
          url: '/pages/home/<USER>'
        });
      }
    });
  },

  // 复制功能 - 使用双弹窗效果（复制按钮 - 使用C类音效）
  copyText() {
    console.log('=== more页面复制功能被调用 ===');

    // 播放短促滴声音效
    if (this.soundManager) {
      this.soundManager.playShortBeep();
    }

    try {
      const title = "助你视界清晰";
      const content = this.getArticleContent();
      const fullText = `${title}\n\n${content}`;

      wx.setClipboardData({
        data: fullText,
        success: () => {
          console.log('复制成功，内容长度:', fullText.length);
          // 系统Toast提示
          wx.showToast({
            title: '内容已复制',
            icon: 'success',
            duration: 1500
          });
          
          // 同时显示自定义弹窗
          this.setData({
            showCopyModal: true
          });
        },
        fail: (err) => {
          console.error('复制失败:', err);
          wx.showModal({
            title: '复制失败',
            content: '复制功能暂时不可用，请稍后重试。',
            showCancel: false,
            confirmText: '知道了',
            confirmColor: '#ff6b6b'
          });
        }
      });
    } catch (error) {
      console.error('复制功能执行出错:', error);
      wx.showToast({
        title: '复制功能异常',
        icon: 'none',
        duration: 2000
      });
    }
  },

  // 增大字体 - 从txt页复制（字体+按钮 - 使用C类音效）
  increaseFontSize() {
    // 播放短促滴声音效
    if (this.soundManager) {
      this.soundManager.playShortBeep();
    }

    const currentSize = this.data.fontSize;
    if (currentSize < 70) {
      this.setData({
        fontSize: Math.min(currentSize + 4, 70)
      });
      wx.showToast({
        title: '字体已放大',
        icon: 'success',
        duration: 1000
      });
    } else {
      wx.showToast({
        title: '字体已达最大',
        icon: 'none',
        duration: 1000
      });
    }
  },

  // 减小字体 - 从txt页复制（字体-按钮 - 使用C类音效）
  decreaseFontSize() {
    // 播放短促滴声音效
    if (this.soundManager) {
      this.soundManager.playShortBeep();
    }

    const currentSize = this.data.fontSize;
    if (currentSize > 32) {
      this.setData({
        fontSize: Math.max(currentSize - 4, 32)
      });
      wx.showToast({
        title: '字体已缩小',
        icon: 'success',
        duration: 1000
      });
    } else {
      wx.showToast({
        title: '字体已达最小',
        icon: 'none',
        duration: 1000
      });
    }
  },

  // 复制邮箱功能
  copyEmail() {
    wx.setClipboardData({
      data: '<EMAIL>',
      success: () => {
        wx.showToast({
          title: '邮箱已复制',
          icon: 'success',
          duration: 1500
        });
      },
      fail: (err) => {
        console.error('复制邮箱失败:', err);
        wx.showToast({
          title: '复制失败，请重试',
          icon: 'none',
          duration: 1500
        });
      }
    });
  },






// 获取文章内容的方法
getArticleContent() {
  return `　　在生活中，我经常看到老人们为了看清药盒上的小字，要么眯着眼使劲凑上前，要么四处找放大镜，可即便这样，还是常常看错剂量。有次邻居奶奶就因为看不清说明书，差点吃错了药。这些场景让我揪心不已，于是我决定开发一款能帮老人看清文字、看懂图片，还能把信息内容朗读出来的微信小程序。有了想法后，我立刻开始行动。

　　但开发小程序远比想象中复杂，整个开发过程中困难重重，说是闯过了九九八十一难是一点都没有夸张。有好几次调试到深夜，面对满屏报错，我都绝望了想要放弃。不过，不服输的我，一想到能帮老人们解决实际困难，我就充满了干劲，决心不管遇到多大的阻碍，都要把这个小程序做出来。

　　我这样一个爱挑战的初中女生，在前面十几年人生里，总喜欢去尝试各种有难度的事。之前凭借微视频创作拿到全国学生信息素养提升实践活动总决赛大奖时，我以为那已是一件不小的挑战，直到开始开发这个微信小程序，才发现这比拿大奖难了好多倍。

　　这一路走来，实属不易，感谢即时设计和CodeFun帮我搞定了UI界面；感谢智普和DeepSeek大模型帮我攻克了图像识别与理解的难关；感谢微信开发工具助力解决小程序调试问题；感谢豆包和腾讯ima坚守岗位随时帮我记录和解答各种难题；最后纠结了半天，决定还是要感谢下所有国内外几乎所有各大AI编程助手，我统统爱过、恨过，说多了全是泪。如果没有强大的Augment的出现，我想我会还要伤心很久。

　　经过无数次的尝试和失败，这个小程序终于完成了。虽然过程艰辛，但看到它能真正帮助到需要的人，一切都值得了。希望这个小程序能让更多老人朋友的生活变得更加便利，也希望能激励更多年轻人去关注和解决身边的实际问题。`;
},






  // 咨询建议联系作者功能 - 一键复制并显示指引
  openCustomerService() {
    const wechatId = 'ABC123';

    // 复制微信号
    wx.setClipboardData({
      data: wechatId,
      success: () => {
        console.log('微信号复制成功:', wechatId);
        // 立即显示自定义弹窗，位置在上方避开系统提示
        this.setData({
          showCustomModal: true
        });
      },
      fail: (err) => {
        console.error('微信号复制失败:', err);
        // 复制失败时的降级方案
        wx.showModal({
          title: '复制失败',
          content: '复制失败，请稍后重试。如果问题持续，请尝试重启小程序。',
          showCancel: false,
          confirmText: '知道了'
        });
      }
    });
  },

  // 隐藏客服弹窗
  hideCustomModal() {
    this.setData({
      showCustomModal: false
    });
  },

  // 隐藏复制弹窗
  hideCopyModal() {
    this.setData({
      showCopyModal: false
    });
  },

  // 阻止弹窗背景滚动
  preventTouchMove() {
    return false;
  },

  onUnload() {
    // 页面卸载时停止朗读并清理资源
    if (this.audioContext) {
      this.audioContext.stop();
      this.audioContext.destroy();
      this.audioContext = null;
    }
  },

});

















