// 按钮对齐测试页面
Page({
  data: {
    translateY: -2, // 垂直偏移量
    lineHeight: 0.9, // 行高
    showGrid: false // 是否显示网格线
  },

  onLoad() {
    // 获取全局音效管理器
    const app = getApp();
    this.soundManager = app.globalData.soundManager;
  },

  // 调整垂直偏移
  adjustTranslateY(e) {
    const direction = e.currentTarget.dataset.direction;
    let newValue = this.data.translateY;
    
    if (direction === 'up') {
      newValue -= 1;
    } else {
      newValue += 1;
    }
    
    this.setData({
      translateY: newValue
    });
    
    // 播放音效
    if (this.soundManager) {
      this.soundManager.playShortBeep();
    }
  },

  // 调整行高
  adjustLineHeight(e) {
    const direction = e.currentTarget.dataset.direction;
    let newValue = this.data.lineHeight;
    
    if (direction === 'decrease') {
      newValue = Math.max(0.1, newValue - 0.1);
    } else {
      newValue = Math.min(2.0, newValue + 0.1);
    }
    
    this.setData({
      lineHeight: Math.round(newValue * 10) / 10
    });
    
    // 播放音效
    if (this.soundManager) {
      this.soundManager.playShortBeep();
    }
  },

  // 切换网格显示
  toggleGrid() {
    this.setData({
      showGrid: !this.data.showGrid
    });
    
    // 播放音效
    if (this.soundManager) {
      this.soundManager.playShortBeep();
    }
  },

  // 重置参数
  resetParams() {
    this.setData({
      translateY: -2,
      lineHeight: 0.9
    });
    
    // 播放音效
    if (this.soundManager) {
      this.soundManager.playShortBeep();
    }
  },

  // 应用到主页面
  applyToHome() {
    wx.showModal({
      title: '应用设置',
      content: `将当前参数应用到主页面？\n垂直偏移: ${this.data.translateY}rpx\n行高: ${this.data.lineHeight}`,
      confirmText: '应用',
      cancelText: '取消',
      success: (res) => {
        if (res.confirm) {
          // 这里可以将参数保存到全局数据或本地存储
          wx.showToast({
            title: '参数已记录，请手动更新CSS',
            icon: 'none',
            duration: 3000
          });
        }
      }
    });
    
    // 播放音效
    if (this.soundManager) {
      this.soundManager.playShortBeep();
    }
  },

  // 测试按钮点击
  testButtonClick() {
    // 播放A类音效
    if (this.soundManager) {
      this.soundManager.playPianoChord();
    }
  },

  // 返回首页
  goBack() {
    // 播放音效
    if (this.soundManager) {
      this.soundManager.playShortBeep();
    }
    
    wx.navigateBack({
      delta: 1,
      fail: (err) => {
        console.error('返回失败:', err);
        wx.navigateTo({
          url: '/pages/home/<USER>'
        });
      }
    });
  }
});
