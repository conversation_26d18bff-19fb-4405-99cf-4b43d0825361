<!--音效测试页面-->
<view class="container">
  <view class="header">
    <text class="title">🎵 音效测试页面</text>
    <text class="status">状态: {{soundStatus}}</text>
  </view>

  <view class="test-section">
    <text class="section-title">音效测试</text>
    
    <button class="test-btn startup" bindtap="testStartupSound">
      🎹 测试开机音效
      <text class="btn-desc">快节奏钢琴旋律 (1.2秒) - Splash页面</text>
    </button>
    
    <button class="test-btn sound-a" bindtap="testSoundA">
      🎵 测试A类音效
      <text class="btn-desc">钢琴和弦 - 拍字/传字/拍图/传图</text>
    </button>
    
    <button class="test-btn sound-c" bindtap="testSoundC">
      🔔 测试C类音效
      <text class="btn-desc">短促滴声 - 解释/字体/复制/重来/返回</text>
    </button>
    
    <button class="test-btn vibration" bindtap="testVibration">
      📳 测试振动功能
      <text class="btn-desc">中等强度振动反馈</text>
    </button>
  </view>

  <view class="control-section">
    <text class="section-title">控制选项</text>
    
    <button class="control-btn toggle" bindtap="toggleSound">
      🔊 切换音效开关
    </button>
    
    <button class="control-btn clear" bindtap="clearResults">
      🗑️ 清空测试记录
    </button>
  </view>

  <view class="results-section" wx:if="{{testResults.length > 0}}">
    <text class="section-title">测试记录</text>
    
    <view class="result-item" wx:for="{{testResults}}" wx:key="id">
      <view class="result-header">
        <text class="result-type">{{item.type}}</text>
        <text class="result-time">{{item.timestamp}}</text>
      </view>
      <text class="result-desc">{{item.description}}</text>
    </view>
  </view>

  <view class="footer">
    <button class="back-btn" bindtap="goBack">
      ← 返回首页
    </button>
  </view>
</view>
