Page({
  data: {
    currentYear: new Date().getFullYear()
  },

  onLoad: function (options) {
    // 获取全局音效管理器
    const app = getApp();
    this.soundManager = app.globalData.soundManager;
    
    // 播放开机音效（钢琴问候）
    setTimeout(() => {
      if (this.soundManager) {
        this.soundManager.playPianoGreeting();
      }
    }, 500); // 延迟500ms播放，让页面先加载完成

    // 设置定时器跳转到主页面
    setTimeout(() => {
      wx.navigateTo({
        url: '/pages/home/<USER>'
      });
    }, 3000); // 3秒后跳转
  },

  onShow: function () {
    // 页面显示时的逻辑
  },

  onHide: function () {
    // 页面隐藏时的逻辑
  },

  // 复制邮箱功能
  copyEmail() {
    wx.setClipboardData({
      data: '<EMAIL>',
      success: () => {
        wx.showToast({
          title: '邮箱已复制',
          icon: 'success',
          duration: 1500
        });
      },
      fail: (err) => {
        console.error('复制邮箱失败:', err);
        wx.showToast({
          title: '复制失败',
          icon: 'none'
        });
      }
    });
  },

  onShareAppMessage() {
    return {
      title: '老人AI放大镜',
      path: '/pages/splash/splash',
      imageUrl: '/images/LOGOfont_1.png'
    };
  }
});


