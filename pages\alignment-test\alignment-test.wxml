<view class="page {{showGrid ? 'show-grid' : ''}}">
  <view class="header">
    <text class="title">按钮对齐测试</text>
    <text class="subtitle">调整参数以获得完美的文字居中效果</text>
  </view>

  <!-- 测试按钮组 -->
  <view class="test-buttons">
    <!-- 看字按钮测试 -->
    <view class="test-btn-container">
      <view class="flex-row justify-center items-center btn-component" bindtap="testButtonClick">
        <text class="btn-text" style="line-height: {{lineHeight}}; transform: translateY({{translateY}}rpx);">看字</text>
        <image style="position: relative; left: 14rpx;" class="btn-image ml-8" src="/images/grandpa.png" />
      </view>
      <text class="btn-label">看字按钮</text>
    </view>

    <!-- 看图按钮测试 -->
    <view class="test-btn-container">
      <view class="flex-row justify-center items-center btn-component" bindtap="testButtonClick">
        <image style="position: relative; left: -21rpx;" class="btn-image" src="/images/grandma.png" />
        <text class="btn-text" style="line-height: {{lineHeight}}; transform: translateY({{translateY}}rpx);">看图</text>
      </view>
      <text class="btn-label">看图按钮</text>
    </view>

    <!-- 更多按钮测试 -->
    <view class="test-btn-container">
      <view class="flex-row justify-center items-center btn-component" bindtap="testButtonClick">
        <text class="btn-text" style="line-height: {{lineHeight}}; transform: translateY({{translateY}}rpx);">更多</text>
        <image style="position: relative; left: 14rpx;" class="btn-image ml-8" src="/images/robot.png" />
      </view>
      <text class="btn-label">更多按钮</text>
    </view>
  </view>

  <!-- 参数调节面板 -->
  <view class="control-panel">
    <view class="control-section">
      <text class="control-title">垂直偏移: {{translateY}}rpx</text>
      <view class="control-buttons">
        <button class="control-btn" bindtap="adjustTranslateY" data-direction="up">↑ 上移</button>
        <button class="control-btn" bindtap="adjustTranslateY" data-direction="down">↓ 下移</button>
      </view>
    </view>

    <view class="control-section">
      <text class="control-title">行高: {{lineHeight}}</text>
      <view class="control-buttons">
        <button class="control-btn" bindtap="adjustLineHeight" data-direction="decrease">- 减小</button>
        <button class="control-btn" bindtap="adjustLineHeight" data-direction="increase">+ 增大</button>
      </view>
    </view>

    <view class="control-section">
      <text class="control-title">辅助工具</text>
      <view class="control-buttons">
        <button class="control-btn {{showGrid ? 'active' : ''}}" bindtap="toggleGrid">
          {{showGrid ? '隐藏' : '显示'}}网格
        </button>
        <button class="control-btn" bindtap="resetParams">重置参数</button>
      </view>
    </view>
  </view>

  <!-- 操作按钮 -->
  <view class="action-buttons">
    <button class="action-btn primary" bindtap="applyToHome">应用到主页面</button>
    <button class="action-btn secondary" bindtap="goBack">返回首页</button>
  </view>

  <!-- 参数显示 -->
  <view class="params-display">
    <text class="params-text">当前参数: translateY({{translateY}}rpx), line-height: {{lineHeight}}</text>
  </view>
</view>
