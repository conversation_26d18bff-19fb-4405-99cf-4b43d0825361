Page({
  data: {},

  onLoad() {
    // 获取全局音效管理器
    const app = getApp();
    this.soundManager = app.globalData.soundManager;
  },

  goToTxt() {
    // 播放短促滴声音效（导航按钮使用C类音效）
    if (this.soundManager) {
      this.soundManager.playShortBeep();
    }

    wx.navigateTo({
      url: '/pages/txt/txt',
      fail: (err) => {
        console.error('导航到看字页面失败:', err);
        wx.showToast({
          title: '页面跳转失败',
          icon: 'none'
        });
      }
    });
  },

  goToPic() {
    // 播放短促滴声音效（导航按钮使用C类音效）
    if (this.soundManager) {
      this.soundManager.playShortBeep();
    }

    wx.navigateTo({
      url: '/pages/pic/pic',
      fail: (err) => {
        console.error('导航到看图页面失败:', err);
        wx.showToast({
          title: '页面跳转失败',
          icon: 'none'
        });
      }
    });
  },

  goToMore() {
    // 播放短促滴声音效（导航按钮使用C类音效）
    if (this.soundManager) {
      this.soundManager.playShortBeep();
    }

    wx.navigateTo({
      url: '/pages/more/more',
      fail: (err) => {
        console.error('导航到更多页面失败:', err);
        wx.showToast({
          title: '页面跳转失败',
          icon: 'none'
        });
      }
    });
  },



  onShareAppMessage() {
    return {
      title: '老人AI放大镜',
      path: '/pages/home/<USER>',
      imageUrl: '/images/LOGOfont.png'
    };
  }
});

