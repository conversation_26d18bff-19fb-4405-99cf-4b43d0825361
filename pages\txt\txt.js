// 引入动态提示工具
const { startDynamicTip, stopDynamicTip } = require('../../utils/dynamicTips.js');

Page({
  data: {
    textContent: "",
    fontSize: 52,
    showResult: false,
    isProcessing: false,
    loadingText: "",
    tipIntervalId: null,
    isExplained: false,
    showCopyModal: false
    // 删除了 isReading: false
  },

  onLoad() {
    // 获取全局音效管理器
    const app = getApp();
    this.soundManager = app.globalData.soundManager;
  },

  // 拍照功能（拍字按钮 - 使用A类音效）
  takePhoto() {
    if (this.data.isProcessing) return;

    // 播放钢琴和弦音效
    if (this.soundManager) {
      this.soundManager.playPianoChord();
    }

    wx.chooseImage({
      count: 1,
      sizeType: ["compressed"],
      sourceType: ['camera'],
      success: (res) => {
        console.log('拍照成功:', res);
        this.processImage(res.tempFilePaths[0], 'ocr');
      },
      fail: (err) => {
        console.error('拍照失败:', err);
        wx.showToast({
          title: '拍照失败，请重试',
          icon: 'none',
          duration: 2000
        });
      }
    });
  },

  // 选择图片功能（传字按钮 - 使用A类音效）
  chooseImage() {
    if (this.data.isProcessing) return;

    // 播放钢琴和弦音效
    if (this.soundManager) {
      this.soundManager.playPianoChord();
    }

    wx.chooseImage({
      count: 1,
      sizeType: ["compressed"],
      sourceType: ['album'],
      success: (res) => {
        console.log('选择图片成功:', res);
        this.processImage(res.tempFilePaths[0], 'ocr');
      },
      fail: (err) => {
        console.error('选择图片失败:', err);
        wx.showToast({
          title: '选择图片失败，请重试',
          icon: 'none',
          duration: 2000
        });
      }
    });
  },

  // 处理图片 - 真实的AI识别
  processImage(imagePath, taskType = 'ocr') {
    // 隐藏初始界面，显示结果区域
    this.setData({
      // showResult: true,  // 移除这行
      isProcessing: true,
      fontSize: 52  // 重置字体大小为默认值
    });

    wx.showLoading({
      title: "AI正在思考..."
    });

    // 先上传图片到云存储
    const cloudPath = `temp-images/${Date.now()}-${Math.random().toString(36).substr(2, 9)}.jpg`;

    wx.cloud.uploadFile({
      cloudPath: cloudPath,
      filePath: imagePath,
      success: (uploadRes) => {
        console.log('图片上传成功:', uploadRes);

        // 调用云函数进行AI识别
        wx.cloud.callFunction({
          name: "aiProxy",
          data: {
            taskType: taskType,
            imagePath: uploadRes.fileID  // 使用云存储文件ID
          },
          success: (res) => {
            console.log('AI识别成功:', res);

            if (res.result.success) {
              // 显示结果到灰色文本框 - 使用formatTextWithIndent函数
              wx.hideLoading();
              this.setData({
                textContent: this.formatTextWithIndent(res.result.content),  // 调用格式化函数
                fontSize: 52,
                isProcessing: false,
                showResult: true,
                currentTitle: '文字识别结果'
              });
            } else {
              // 处理错误
              wx.hideLoading();
              this.handleError(res.result.error, res.result.errorCode);
            }
          },
          fail: (err) => {
            console.error('云函数调用失败:', err);
            wx.hideLoading();
            this.handleError('网络连接失败，请检查网络后重试', 'NETWORK_ERROR');
          }
        });
      },
      fail: (uploadErr) => {
        console.error('图片上传失败详细信息:', uploadErr);
        console.error('错误代码:', uploadErr.errCode);
        console.error('错误信息:', uploadErr.errMsg);

        let errorMsg = '图片上传失败，请重试';
        if (uploadErr.errMsg) {
          errorMsg = `上传失败: ${uploadErr.errMsg}`;
        }

        wx.hideLoading();
        this.handleError(errorMsg, 'UPLOAD_ERROR');
      }
    });
  },



  // 启动动态提示
  startDynamicTip(pageType) {
    const tipIntervalId = startDynamicTip(pageType, (tipText) => {
      this.setData({
        loadingText: tipText
      });
    });

    this.setData({
      tipIntervalId: tipIntervalId
    });
  },

  // 停止动态提示
  stopDynamicTip() {
    if (this.data.tipIntervalId) {
      stopDynamicTip(this.data.tipIntervalId);
      this.setData({
        tipIntervalId: null,
        loadingText: ""
      });
    }
  },

  // 错误处理
  handleError(errorMessage, errorCode) {
    console.error('处理错误:', errorMessage, errorCode);

    this.setData({
      textContent: "",
      isProcessing: false,
      showResult: false
    });

    // 根据错误类型显示不同提示
    let title = '处理失败';
    let content = errorMessage;
    
    if (errorCode === 'INVALID_API_KEY') {
      title = 'API密钥错误';
      content = 'API密钥无效或已过期，请联系开发者更新API密钥。';
    } else if (errorCode === 'FILE_TOO_LARGE') {
      title = '文件过大';
      content = '图片文件过大，请压缩后重试。';
    } else if (errorCode === 'NETWORK_TIMEOUT') {
      title = '网络超时';
      content = '网络连接超时，请检查网络后重试。\n\n建议：\n1. 检查网络连接\n2. 稍后重试\n3. 尝试处理较小的图片';
    } else if (errorCode === 'FUNCTION_TIMEOUT') {
      title = '处理超时';
      content = 'AI处理时间过长，请稍后重试。\n\n建议：\n1. 等待1-2分钟后重试\n2. 尝试处理较小的图片\n3. 检查网络连接';
    } else if (errorCode === 'NETWORK_ERROR') {
      title = '网络错误';
      content = '网络连接失败，请检查网络后重试。\n\n建议：\n1. 检查WiFi或移动网络\n2. 尝试切换网络\n3. 稍后重试';
    }

    wx.showModal({
      title: title,
      content: content,
      showCancel: true,
      confirmText: '重试',
      cancelText: '取消',
      confirmColor: '#00a946',
      success: (res) => {
        if (res.confirm) {
          // 用户选择重试，可以重新显示选择界面
          this.setData({
            showResult: false,
            showButtons: true
          });
        }
      }
    });
  },

  // 显示使用提示
  showUsageTip() {
    wx.showModal({
      title: '使用提示',
      content: '拍照或上传文字照片，AI帮你识别文字，还能放大显示、朗读或解释',
      showCancel: false,
      confirmText: '知道了',
      confirmColor: '#00a946'
    });
  },

  // 增大字体（字体+按钮 - 使用C类音效）
  increaseFontSize() {
    // 播放短促滴声音效
    if (this.soundManager) {
      this.soundManager.playShortBeep();
    }

    const currentSize = this.data.fontSize;
    if (currentSize < 70) {
      this.setData({
        fontSize: Math.min(currentSize + 4, 70)
      });
      wx.showToast({
        title: '字体已放大',
        icon: 'success',
        duration: 1000
      });
    } else {
      wx.showToast({
        title: '字体已达最大',
        icon: 'none',
        duration: 1000
      });
    }
  },

  // 减小字体（字体-按钮 - 使用C类音效）
  decreaseFontSize() {
    // 播放短促滴声音效
    if (this.soundManager) {
      this.soundManager.playShortBeep();
    }

    const currentSize = this.data.fontSize;
    if (currentSize > 32) {
      this.setData({
        fontSize: Math.max(currentSize - 4, 32)
      });
      wx.showToast({
        title: '字体已缩小',
        icon: 'success',
        duration: 1000
      });
    } else {
      wx.showToast({
        title: '字体已达最小',
        icon: 'none',
        duration: 1000
      });
    }
  },

  // 清理文本，移除emoji和特殊符号 - 优化版本
  cleanTextForReading(text) {
    if (!text) {
      console.log('输入文本为空');
      return '';
    }

    console.log('开始清理文本，原始长度:', text.length);
    console.log('原始文本前100字:', text.substring(0, 100));

    // 第一步：移除emoji符号
    let cleanText = text.replace(/[\u{1F600}-\u{1F64F}]|[\u{1F300}-\u{1F5FF}]|[\u{1F680}-\u{1F6FF}]|[\u{1F1E0}-\u{1F1FF}]|[\u{2600}-\u{26FF}]|[\u{2700}-\u{27BF}]/gu, '');
    console.log('移除emoji后长度:', cleanText.length);

    // 第二步：保留中文、英文、数字和基本标点符号
    cleanText = cleanText.replace(/[^\u4e00-\u9fa5a-zA-Z0-9\s，。！？；：""''（）【】《》\-、]/g, '');
    console.log('移除特殊符号后长度:', cleanText.length);

    // 第三步：清理多余空格，但保留句子结构
    cleanText = cleanText.replace(/\s+/g, ' ').trim();
    console.log('清理空格后长度:', cleanText.length);

    // 第四步：确保文本不为空
    if (cleanText.length === 0) {
      console.log('清理后文本为空，使用备用文本');
      cleanText = '这是一段测试文本，用于验证朗读功能。';
    }

    console.log('最终清理结果:');
    console.log('- 长度:', cleanText.length);
    console.log('- 前50字:', cleanText.substring(0, 50));

    return cleanText;
  },

  // 朗读功能 - 微信TTS + 系统朗读指引
  toggleReading() {
    console.log('=== txt页面朗读功能被调用 ===');

    if (this.data.isReading) {
      // 停止朗读
      wx.stopTextToSpeech({
        success: () => {
          wx.showToast({
            title: '朗读已停止',
            icon: 'none',
            duration: 1500
          });
        }
      });
      this.setData({ isReading: false });
      return;
    }

    // 检查是否有内容
    if (!this.data.textContent || this.data.textContent.includes('请稍候') || this.data.isProcessing) {
      wx.showToast({
        title: '暂无内容可朗读',
        icon: 'none',
        duration: 2000
      });
      return;
    }

    const cleanText = this.cleanTextForReading(this.data.textContent);
  
    if (!cleanText || cleanText.length < 5) {
      wx.showToast({
        title: '没有可朗读的文字内容',
        icon: 'none',
        duration: 2000
      });
      return;
    }

    this.setData({ isReading: true });

    // 尝试微信TTS
    wx.textToSpeech({
      lang: "zh_CN",
      text: cleanText,
      success: (res) => {
        console.log("✅ 微信TTS调用成功:", res);
        wx.showToast({
          title: '开始朗读',
          icon: 'success',
          duration: 1500
        });
      },
      fail: (err) => {
        console.error("❌ 微信TTS调用失败:", err);
        this.setData({ isReading: false });
        
        // 显示精美的系统朗读指引
        this.showSystemReadingGuide();
      },
      complete: () => {
        setTimeout(() => {
          this.setData({ isReading: false });
        }, 2000);
      }
    });
  },

  // 显示系统朗读指引
  showSystemReadingGuide() {
    const systemInfo = wx.getSystemInfoSync();
    const isAndroid = systemInfo.system.toLowerCase().includes('android');
    const isIOS = systemInfo.system.toLowerCase().includes('ios');

    let title = '📢 朗读功能指引';
    let content = '';
    let cancelText = '系统设置';
    let confirmText = '我知道了';

    if (isAndroid) {
      content = `🔊 您的设备可以这样开启朗读：

📱 方法一：TalkBack朗读
• 打开手机"设置"
• 找到"无障碍"或"辅助功能"  
• 开启"TalkBack"或"语音助手"
• 返回小程序，触摸文字即可朗读

📱 方法二：选择朗读
• 长按文字内容
• 选择"朗读"或"语音播放"

💡 温馨提示：开启后可以朗读所有文字内容`;

    } else if (isIOS) {
      content = `🔊 您的iPhone可以这样开启朗读：

📱 方法一：朗读所选项
• 打开手机"设置"
• 进入"辅助功能"
• 选择"朗读内容"
• 开启"朗读所选项"
• 返回小程序，选中文字即可朗读

📱 方法二：朗读屏幕
• 同样在"朗读内容"中
• 开启"朗读屏幕"
• 双指从屏幕顶部向下滑动

💡 温馨提示：开启后所有应用都可使用`;

    } else {
      content = `🔊 您的设备可以这样开启朗读功能：

📱 通用方法：
• 进入手机系统设置
• 找到"无障碍"或"辅助功能"
• 开启相关朗读功能
• 返回小程序使用

📝 替代方案：
• 调大字体便于阅读
• 手动朗读给家人听

💡 如需帮助，请咨询家人或朋友`;
    }

    wx.showModal({
      title: title,
      content: content,
      showCancel: true,
      cancelText: cancelText,
      confirmText: confirmText,
      success: (res) => {
        if (res.cancel) {
          // 用户想查看详细设置步骤
          this.showDetailedSystemGuide(isAndroid, isIOS);
        } else {
          // 用户了解了，提示调整字体
          wx.showToast({
            title: '您也可以使用+/-按钮调整字体大小',
            icon: 'none',
            duration: 3000
          });
        }
      }
    });
  },

  // 显示详细系统设置指引
  showDetailedSystemGuide(isAndroid, isIOS) {
    let title = '📖 详细设置步骤';
    let content = '';

    if (isAndroid) {
      content = `🤖 Android设备设置步骤：

1️⃣ 打开手机"设置"应用
2️⃣ 滑动找到"无障碍"选项
   (有些手机叫"辅助功能")
3️⃣ 点击进入无障碍设置
4️⃣ 找到"TalkBack"或"语音助手"
5️⃣ 点击开启该功能
6️⃣ 返回微信小程序
7️⃣ 轻触文字即可听到朗读

⚠️ 注意：开启后操作方式会改变
关闭方法：重复上述步骤关闭即可`;

    } else if (isIOS) {
      content = `🍎 iPhone设备设置步骤：

1️⃣ 打开手机"设置"应用
2️⃣ 滑动找到"辅助功能"
3️⃣ 点击"朗读内容"
4️⃣ 开启"朗读所选项"
5️⃣ 返回微信小程序
6️⃣ 选中文字后点击"朗读"

🔄 朗读屏幕功能：
• 同时开启"朗读屏幕"
• 双指从屏幕顶部向下滑动
• 即可朗读整个屏幕内容

💡 这些功能对所有应用都有效`;

    } else {
      content = `📱 通用设置指引：

🔍 寻找设置：
• 手机设置 → 无障碍功能
• 或：设置 → 辅助功能
• 或：设置 → 系统 → 无障碍

🎯 寻找功能：
• TalkBack (Android)
• 语音助手
• 朗读功能
• 选择朗读

❓ 如果找不到：
• 询问家人朋友帮助
• 联系手机厂商客服
• 或直接手动阅读内容`;
    }

    wx.showModal({
      title: title,
      content: content,
      showCancel: false,
      confirmText: '明白了',
      success: () => {
        wx.showToast({
          title: '设置完成后就可以朗读啦！',
          icon: 'none',
          duration: 2000
        });
      }
    });
  },

  // 解释文本 - 转换为易懂版（解释按钮 - 使用C类音效）
  explainText() {
    console.log('=== 解释功能被调用 ===');

    // 播放短促滴声音效
    if (this.soundManager) {
      this.soundManager.playShortBeep();
    }

    console.log('当前文本内容:', this.data.textContent);
    console.log('文本长度:', this.data.textContent ? this.data.textContent.length : 0);
    console.log('是否正在处理:', this.data.isProcessing);

    if (!this.data.textContent || this.data.textContent.includes('请稍候') || this.data.isProcessing) {
      console.log('解释功能被阻止：无内容或正在处理中');
      wx.showToast({
        title: '暂无内容可解释',
        icon: 'none'
      });
      return;
    }

    const currentText = this.data.textContent;
    console.log('准备解释的文本:', currentText.substring(0, 100) + '...');

    this.setData({
      isProcessing: true
    });

    wx.showLoading({
      title: "AI正在思考..."
    });

    // 调用云函数进行文本简化，增加重试机制
    this.callExplainWithRetry(currentText, 3); // 最多重试3次
  },

  // 带重试机制的解释调用
  callExplainWithRetry(text, retryCount) {
    // 如果文本过长，可能导致API超时，这里进行截断处理
    const maxLength = 2000; // 设置最大字符数
    let processText = text;
    let isTruncated = false;
    
    if (text.length > maxLength) {
      processText = text.substring(0, maxLength) + "...";
      isTruncated = true;
      console.log(`文本过长(${text.length}字符)，已截断至${maxLength}字符`);
    }

    console.log(`开始调用解释功能，剩余重试次数: ${retryCount}`);
    
    wx.cloud.callFunction({
      name: "aiProxy",
      data: {
        taskType: "simplify-text",
        prompt: processText
      },
      success: (res) => {
        wx.hideLoading();
        console.log('解释功能返回结果:', res.result);

        if (res.result && res.result.success) {
          // 处理AI返回的内容，移除固定前缀
          let content = res.result.content;

          // 移除常见的AI回复前缀
          const prefixesToRemove = [
            /^好的，以下是老人易懂版的转换：\s*/,
            /^以下是老人易懂版的转换：\s*/,
            /^老人易懂版：\s*/,
            /^易懂版：\s*/,
            /^转换结果：\s*/
          ];

          prefixesToRemove.forEach(prefix => {
            content = content.replace(prefix, '');
          });

          // 如果原文本被截断，添加提示信息
          if (isTruncated) {
            content += "\n\n(注：原文过长，此为部分内容的解释)";
          }

          // 显示易懂版文本（不添加标题前缀）
          this.setData({
            textContent: this.formatTextWithIndent(content),
            isProcessing: false,
            isExplained: true  // 标记为已解释状态
          });
        } else {
          this.handleExplainError(text, retryCount, res.result ? res.result.error : '未知错误', res.result ? res.result.errorCode : 'UNKNOWN');
        }
      },
      fail: (err) => {
        console.error('文本简化失败:', err);
        this.handleExplainError(text, retryCount, '网络连接失败', 'NETWORK_ERROR');
      }
    });
  },

  // 处理解释功能的错误和重试
  handleExplainError(text, retryCount, error, errorCode) {
    console.log(`解释功能错误: ${error}, 错误代码: ${errorCode}, 剩余重试次数: ${retryCount-1}`);
    
    if (retryCount > 1) {
      // 还有重试次数，自动重试
      wx.showToast({
        title: `正在重试(${4-retryCount}/3)...`,
        icon: 'none',
        duration: 1500
      });
      
      setTimeout(() => {
        this.callExplainWithRetry(text, retryCount - 1);
      }, 3000); // 3秒后重试，避免频繁请求
    } else {
      // 重试次数用完，显示错误
      wx.hideLoading();
      this.setData({
        isProcessing: false
      });

      // 根据错误类型提供更详细的错误信息
      let errorMessage = '网络连接不稳定或服务繁忙，请稍后再试';
      let errorTitle = '解释功能暂时不可用';
      
      if (errorCode === 'INVALID_API_KEY') {
        errorMessage = 'API密钥无效或已过期，请联系开发者更新API密钥。\n\n解决方案：\n1. 检查网络连接\n2. 稍后重试\n3. 如果问题持续，请联系开发者';
        errorTitle = 'API密钥错误';
      } else if (errorCode === 'NETWORK_ERROR' || errorCode === 'NETWORK_TIMEOUT') {
        errorMessage = '网络连接不稳定，请检查您的网络连接后重试。\n\n建议：\n1. 检查WiFi或移动网络\n2. 尝试切换网络\n3. 稍后重试';
        errorTitle = '网络连接错误';
      } else if (errorCode === 'SERVER_ERROR') {
        errorMessage = 'AI服务器繁忙，请稍后再试。\n\n建议：\n1. 等待1-2分钟后重试\n2. 尝试处理较短的文本\n3. 如果问题持续，请稍后再试';
        errorTitle = '服务器繁忙';
      } else if (text.length > 5000) {
        errorMessage = '文本内容过长，请尝试处理较短的文本。\n\n建议：\n1. 选择较短的文本段落\n2. 分段处理长文本\n3. 每次处理不超过5000字符';
        errorTitle = '文本过长';
      } else if (errorCode === 'UNKNOWN_ERROR') {
        errorMessage = '发生未知错误，请稍后重试。\n\n如果问题持续存在，请：\n1. 重启小程序\n2. 检查网络连接\n3. 联系开发者反馈';
        errorTitle = '未知错误';
      }

      wx.showModal({
        title: errorTitle,
        content: errorMessage,
        showCancel: true,
        confirmText: '重试',
        cancelText: '取消',
        confirmColor: '#00a946',
        success: (res) => {
          if (res.confirm) {
            // 用户选择重试
            this.explainText();
          }
        }
      });
    }
  },

  // 返回首页（返回按钮 - 使用C类音效）
  goBack() {
    // 播放短促滴声音效
    if (this.soundManager) {
      this.soundManager.playShortBeep();
    }

    wx.navigateBack({
      delta: 1,
      fail: (err) => {
        console.error('返回失败:', err);
        wx.navigateTo({
          url: '/pages/home/<USER>'
        });
      }
    });
  },

  // 重新开始 - 完整重置页面状态（重来按钮 - 使用C类音效）
  restart() {
    // 播放短促滴声音效
    if (this.soundManager) {
      this.soundManager.playShortBeep();
    }

    wx.showModal({
      title: '确认重置刷新',
      content: '将已显示内容清除，重置刷新？',
      confirmText: '确认',
      cancelText: '取消',
      confirmColor: '#00a946',
      success: (res) => {
        if (res.confirm) {
          // 停止动态提示
          this.stopDynamicTip();

          // 完整重置页面状态
          this.setData({
            textContent: "",
            fontSize: 36,
            isReading: false,
            showResult: false,
            isProcessing: false,
            loadingText: "",
            tipIntervalId: null
          });

          wx.showToast({
            title: '已重置刷新',
            icon: 'success',
            duration: 1500
          });
        }
      }
    });
  },

  // 分享功能
  shareApp() {
    // 显示分享选项
    wx.showActionSheet({
      itemList: ['分享给微信好友', '分享到朋友圈', '复制分享链接'],
      success: (res) => {
        if (res.tapIndex === 0) {
          // 分享给微信好友
          wx.showShareMenu({
            withShareTicket: true,
            menus: ['shareAppMessage']
          });
          wx.showToast({
            title: '请点击右上角分享',
            icon: 'none',
            duration: 2000
          });
        } else if (res.tapIndex === 1) {
          // 分享到朋友圈
          wx.showShareMenu({
            withShareTicket: true,
            menus: ['shareTimeline']
          });
          wx.showToast({
            title: '请点击右上角分享',
            icon: 'none',
            duration: 2000
          });
        } else if (res.tapIndex === 2) {
          // 复制分享链接
          wx.setClipboardData({
            data: '老人AI放大镜，帮你看清世界！专为老年朋友设计的智能助手，拍照识别文字，AI理解图片内容。',
            success: () => {
              wx.showToast({
                title: '分享内容已复制',
                icon: 'success',
                duration: 2000
              });
            }
          });
        }
      },
      fail: () => {
        wx.showToast({
          title: '分享取消',
          icon: 'none',
          duration: 1000
        });
      }
    });
  },

  onShareAppMessage() {
    return {
      title: '老人AI放大镜，帮你看清世界！',
      path: '/pages/splash/splash',
      imageUrl: '/images/LOGOfont.png'
    };
  },

  // 格式化文本，取消所有缩进
  formatTextWithIndent(text) {
    if (!text) return '';
    
    // 直接返回原文本，不添加任何缩进
    return text.trim();
  },

  // 复制功能 - 模仿more页双弹窗效果（复制按钮 - 使用C类音效）
  copyText() {
    console.log('=== txt页面复制功能被调用 ===');

    // 播放短促滴声音效
    if (this.soundManager) {
      this.soundManager.playShortBeep();
    }

    if (!this.data.textContent || this.data.textContent.includes('请稍候') || this.data.isProcessing) {
      wx.showToast({
        title: '暂无内容可复制',
        icon: 'none',
        duration: 2000
      });
      return;
    }

    wx.setClipboardData({
      data: this.data.textContent,
      success: () => {
        // 系统Toast提示
        wx.showToast({
          title: '内容已复制',
          icon: 'success',
          duration: 1500
        });
        
        // 同时显示自定义弹窗
        this.setData({
          showCopyModal: true
        });
      },
      fail: (err) => {
        console.error('复制失败:', err);
        wx.showModal({
          title: '复制失败',
          content: '复制功能暂时不可用，请稍后重试。',
          showCancel: false,
          confirmText: '知道了',
          confirmColor: '#ff6b6b'
        });
      }
    });
  },

  // 隐藏复制弹窗
  hideCopyModal() {
    this.setData({
      showCopyModal: false
    });
  },

  // 阻止弹窗背景滚动
  preventTouchMove() {
    return false;
  }
});



